import 'package:flutter/material.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    // Sample products data
    final List<Map<String, dynamic>> products = [
      {'name': 'Product 1', 'price': 29.99},
      {'name': 'Product 2', 'price': 39.99},
      {'name': 'Product 3', 'price': 19.99},
      {'name': 'Product 4', 'price': 49.99},
    ];

    return Scaffold(
      appBar: AppBar(title: Text("Products")),
      body: ListView.builder(
        itemCount: products.length,
        itemBuilder: (context, index) {
          final product = products[index];
          return ListTile(
            title: Text(product['name']),
            subtitle: Text("\$${product['price']}"),
            trailing: IconButton(
              icon: Icon(Icons.add_shopping_cart),
              onPressed: () {
                Navigator.pushNamed(context, '/cart');
              },
            ),
          );
        },
      ),
    );
  }
}
