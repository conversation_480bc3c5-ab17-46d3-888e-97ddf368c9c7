import 'dart:convert';
import 'package:flutter/foundation.dart';

class User {
  final String email;
  final String password;

  User({required this.email, required this.password});

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'password': password,
    };
  }

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      email: json['email'],
      password: json['password'],
    );
  }
}

class UserService {
  static final UserService _instance = UserService._internal();
  factory UserService() => _instance;
  UserService._internal();

  final List<User> _users = [];

  Future<bool> register(String email, String password) async {
    await Future.delayed(Duration(seconds: 1));
    
    if (email.isEmpty || password.isEmpty) {
      return false;
    }

    if (!_isValidEmail(email)) {
      return false;
    }

    if (password.length < 6) {
      return false;
    }

    if (_users.any((user) => user.email == email)) {
      return false;
    }

    _users.add(User(email: email, password: password));
    return true;
  }

  Future<bool> login(String email, String password) async {
    await Future.delayed(Duration(seconds: 1));
    
    if (email.isEmpty || password.isEmpty) {
      return false;
    }

    return _users.any((user) => user.email == email && user.password == password);
  }

  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  List<User> getAllUsers() {
    return List.from(_users);
  }

  bool userExists(String email) {
    return _users.any((user) => user.email == email);
  }
}
