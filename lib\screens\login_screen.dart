import 'package:flutter/material.dart';

class LoginPage extends StatelessWidget {
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();

  LoginPage({super.key});

  void login(BuildContext context) {
    if (emailController.text.isNotEmpty && passwordController.text.isNotEmpty) {
      Navigator.pushReplacementNamed(context, '/home');
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Please enter email and password')));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          Text<PERSON><PERSON>(
              controller: emailController,
              decoration: InputDecoration(labelText: "Email")),
          TextField(
              controller: passwordController,
              decoration: InputDecoration(labelText: "Password"),
              obscureText: true),
          ElevatedButton(onPressed: () => login(context), child: Text("Login")),
        ],
      ),
    );
  }
}
