// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDPWxtpNAuE70oRXVUqdjPoFki06fjuy28',
    appId: '1:325701387539:web:5ff700c74a6465068b86e6',
    messagingSenderId: '325701387539',
    projectId: 'shoppingcart-89f2c',
    authDomain: 'shoppingcart-89f2c.firebaseapp.com',
    storageBucket: 'shoppingcart-89f2c.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDPWxtpNAuE70oRXVUqdjPoFki06fjuy28',
    appId: '1:325701387539:android:5ff700c74a6465068b86e6',
    messagingSenderId: '325701387539',
    projectId: 'shoppingcart-89f2c',
    authDomain: 'shoppingcart-89f2c.firebaseapp.com',
    storageBucket: 'shoppingcart-89f2c.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDPWxtpNAuE70oRXVUqdjPoFki06fjuy28',
    appId: '1:325701387539:ios:5ff700c74a6465068b86e6',
    messagingSenderId: '325701387539',
    projectId: 'shoppingcart-89f2c',
    authDomain: 'shoppingcart-89f2c.firebaseapp.com',
    storageBucket: 'shoppingcart-89f2c.firebasestorage.app',
    iosBundleId: 'com.example.flutterApplication1',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDPWxtpNAuE70oRXVUqdjPoFki06fjuy28',
    appId: '1:325701387539:ios:5ff700c74a6465068b86e6',
    messagingSenderId: '325701387539',
    projectId: 'shoppingcart-89f2c',
    authDomain: 'shoppingcart-89f2c.firebaseapp.com',
    storageBucket: 'shoppingcart-89f2c.firebasestorage.app',
    iosBundleId: 'com.example.flutterApplication1',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyDPWxtpNAuE70oRXVUqdjPoFki06fjuy28',
    appId: '1:325701387539:web:5ff700c74a6465068b86e6',
    messagingSenderId: '325701387539',
    projectId: 'shoppingcart-89f2c',
    authDomain: 'shoppingcart-89f2c.firebaseapp.com',
    storageBucket: 'shoppingcart-89f2c.firebasestorage.app',
  );
}
